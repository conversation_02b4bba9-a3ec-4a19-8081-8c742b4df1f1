

Database Schema Documentation (Public Schema) - Refined
Overview

This document outlines the structure and relationships of the tables within the public schema of the AI Navigator platform database. The schema is designed to store information about users, various types of entities (AI tools, agencies, communities, courses, creators, newsletters), their categorization, tags, reviews, badges, and related user activities.

Key Tables and Relationships
Core Entities

users

Description: Stores user profile information, linked to the Supabase auth.users table via auth_user_id.

Key Columns:

id (UUID, PK): Primary key for the user profile record.

auth_user_id (UUID, Unique, FK -> auth.users.id): Links to the authentication user (required).

username (text, unique): User's unique handle (optional).

display_name (text): User's display name.

email (text, not null, unique): User's email address (Requires sync mechanism with auth.users).

role (user_role enum, not null, default 'user'): User role ('user', 'admin', 'moderator').

status (user_status enum, not null, default 'active'): Profile status ('active', 'pending_verification', 'suspended', 'deleted').

technical_level (technical_level enum): User's self-declared technical level ('beginner', 'intermediate', 'advanced', 'variable').

profile_picture_url (text): URL for the user's avatar.

bio (text): User's profile biography.

social_links (jsonb): Stores various social/website links (e.g., {"website": "...", "twitter": "..."}).

created_at, updated_at (timestamptz): Managed timestamps.

last_login (timestamptz): Timestamp of last recorded login.

Relationships:

One-to-One with user_notification_settings.

One-to-Many with reviews, review_votes, user_activity_logs, user_badges, user_saved_entities, user_followed_categories, user_followed_tags, entities (as submitter/verifier).

entity_types

Description: Defines the distinct types of entities listed (e.g., 'AI Tool', 'Agency', 'Community', 'Course', 'Content Creator', 'Newsletter').

Key Columns:

id (UUID, PK): Primary key for the entity type.

name (text, not null, unique): Name of the entity type (e.g., "AI Tool").

slug (text, not null, unique): URL-friendly identifier.

description (text): Description of the type.

icon_url (text): URL for an icon representing the type.

display_order (int, default 0): Controls sort order in UI elements.

created_at, updated_at (timestamptz): Managed timestamps.

Relationships: One-to-Many with entities.

entities

Description: Core table storing common information for all listed entities. Specific details are stored in related entity_details_* tables.

Key Columns:

id (UUID, PK): Primary key for the entity.

entity_type_id (UUID, not null, FK -> entity_types.id): Specifies the type of the entity.

name (text, not null): Name of the entity.

short_description (text): Brief summary for cards/previews.

description (text): Main description content.

logo_url (text): URL for the entity's logo.

website_url (text, not null): Official website URL.

documentation_url (text): Link to documentation.

contact_url (text): Link to contact page/form.

privacy_policy_url (text): Link to privacy policy.

founded_year (int): Year the entity/company was founded.

social_links (jsonb): Stores various social profile links.

status (entity_status enum, not null, default 'pending'): Current status ('pending', 'active', 'archived', 'rejected', 'needs_review').

verified (boolean, default false): Indicates if ownership is verified.

featured (boolean, default false): Admin flag for promotion.

sponsored (boolean, default false): Paid promotion flag.

platform_vetted (boolean, default false): Flag for deeper platform review (future).

avg_rating (float, default 0): Denormalized average rating (Requires trigger/logic based on approved reviews).

review_count (int, default 0): Denormalized count of approved reviews (Requires trigger/logic).

save_count (int, default 0): Denormalized count of user saves (Requires trigger/logic).

click_count (int, default 0): Denormalized count of website/ref clicks (Requires app logic).

review_summary_ai (text): AI-generated summary of recent reviews.

verification_token (uuid, unique): Token used for email verification process.

verified_at (timestamptz): Timestamp when verification occurred.

verified_by (uuid, FK -> auth.users.id): Auth ID of user who performed verification action (if applicable).

affiliate_status (affiliate_status enum, default 'none'): Status of affiliate program relation.

ref_link (text): The actual affiliate link.

user_submitted (boolean, default false): Flag if submitted by a platform user.

submitter_user_id (uuid, FK -> users.id): Profile ID of the user who submitted the entity.

source_url (text): Original URL source if scraped/submitted.

admin_notes (text): Internal notes for platform admins/curators.

vector_embedding (vector(1536)): Numerical representation for semantic search (Requires generation logic).

created_at, updated_at (timestamptz): Managed timestamps.

last_scraped_update, last_manual_update (timestamptz): Timestamps for tracking update sources.

Relationships:

Many-to-One with entity_types.

Many-to-One with users (as submitter).

One-to-Many with reviews, entity_badges, entity_tags, entity_categories, user_saved_entities.

One-to-One with specific entity_details_* tables (e.g., entity_details_tool).

entity_details_* Tables (e.g., entity_details_tool, entity_details_agency, entity_details_course, entity_details_community, entity_details_content_creator, entity_details_newsletter)

Description: Store attributes specific to a particular entity_type. Each links back to the entities table via a shared primary key.

Key Columns:

entity_id (UUID, PK, FK -> entities.id): Links to the core entity record.

Type-specific columns: (e.g., technical_level, has_api, integrations jsonb for tools; services_offered jsonb, industry_focus jsonb for agencies; instructor_name, skill_level for courses; platform, focus_topics jsonb for communities; primary_platform, focus_areas jsonb for creators; frequency, main_topics jsonb for newsletters).

Relationships: One-to-One with entities.

Classification & Taxonomy

categories

Description: Hierarchical categories for organizing entities.

Key Columns:

id (UUID, PK): Primary key for the category.

name (text, not null, unique): Category name.

slug (text, not null, unique): URL-friendly identifier.

description (text): Optional description.

parent_category_id (UUID, FK -> categories.id): Enables hierarchy (NULL for top-level).

icon_url (text): URL for category icon.

display_order (int, default 0): Controls sort order in UI.

created_at, updated_at (timestamptz): Managed timestamps.

Relationships: Many-to-Many with entities (via entity_categories). Can self-reference via parent_category_id. One-to-Many with user_followed_categories.

tags

Description: Stores reusable tags for classifying entities (e.g., 'image-generation', 'no-code').

Key Columns:

id (UUID, PK): Primary key for the tag.

name (text, not null, unique): Tag name.

description (text): Optional context for the tag.

slug (text, not null, unique): URL-friendly identifier.

created_at, updated_at (timestamptz): Managed timestamps.

Relationships: Many-to-Many with entities (via entity_tags). One-to-Many with user_followed_tags.

entity_categories (Junction Table)

Description: Associates entities with relevant categories.

Key Columns:

entity_id (UUID, PK, FK -> entities.id)

category_id (UUID, PK, FK -> categories.id)

Relationships: Implements Many-to-Many between entities and categories.

entity_tags (Junction Table)

Description: Associates entities with relevant tags.

Key Columns:

entity_id (UUID, PK, FK -> entities.id)

tag_id (UUID, PK, FK -> tags.id)

Relationships: Implements Many-to-Many between entities and tags.

Reviews & User Interaction

reviews

Description: Stores user reviews for entities.

Key Columns:

id (UUID, PK): Primary key for the review.

entity_id (UUID, not null, FK -> entities.id): The entity being reviewed.

user_id (UUID, not null, FK -> users.id): The user who wrote the review.

rating (int, not null): Numerical rating (1-5).

title (text): Optional title for the review.

review_text (text): The main content of the review.

status (review_status enum, not null, default 'pending'): Moderation status ('pending', 'approved', 'rejected', 'edited').

helpfulness_score (int, default 0): Denormalized score from votes (Requires trigger/logic based on review_votes).

moderator_user_id (uuid, FK -> users.id): Profile ID of user who moderated.

moderated_at (timestamptz): Timestamp of moderation action.

moderation_notes (text): Internal notes related to moderation.

created_at, updated_at (timestamptz): Managed timestamps.

Relationships: Many-to-One with entities, Many-to-One with users. One-to-Many with review_votes.

user_saved_entities (Junction Table)

Description: Tracks which entities users have saved/bookmarked.

Key Columns:

user_id (UUID, PK, FK -> users.id)

entity_id (UUID, PK, FK -> entities.id)

created_at (timestamptz): When the item was saved.

Relationships: Implements Many-to-Many between users and entities for saves.

Other Supporting Tables (Brief Mention)

badge_types: Defines available badges (e.g., 'Verified Creator').

user_badges: Assigns badges to users.

entity_badges: Assigns badges to entities.

review_votes: Tracks user helpfulness votes on reviews.

user_followed_categories: Tracks categories followed by users.

user_followed_tags: Tracks tags followed by users.

user_notification_settings: Stores user preferences for email/platform notifications.

user_activity_logs: Records significant user actions on the platform.

Timestamp Handling

Most tables include created_at and updated_at columns (type timestamp with time zone).

created_at defaults to NOW() upon insertion.

updated_at is automatically updated to NOW() on row modification via a trigger executing the handle_updated_at() function (applied via CREATE TRIGGER set_*_timestamp ...).

Naming Conventions

Table names are plural and use snake_case (e.g., users, entity_types).

Column names use snake_case (e.g., entity_type_id, created_at).

Primary keys are typically named id (UUID) or use a composite key for junction tables.

Foreign keys follow the pattern [related_table_singular]_id (e.g., entity_type_id references entity_types.id).

Junction tables combine the names of the linked tables (e.g., entity_categories).