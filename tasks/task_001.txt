# Task ID: 1
# Title: Setup Core Infrastructure and Database Schema
# Status: pending
# Dependencies: None
# Priority: high
# Description: Provision Supabase instance, configure Vercel project, and implement the initial database schema for the AI Tool Discovery Platform.
# Details:
1. Provision Supabase instance with appropriate plan for MVP needs
2. Configure Supabase Auth for user authentication
3. Define and migrate the core database schema including tables for: users, resources, categories, tags, resource_categories, resource_tags, reviews, submissions, saved_items, and tool_requests
4. Set up appropriate indexes for performance optimization
5. Configure Vercel project for Next.js frontend deployment
6. Set up cloud container service (e.g., Google Cloud Run) for NestJS backend
7. Establish CI/CD pipelines for both frontend and backend

# Test Strategy:
1. Verify successful connection to Supabase from both frontend and backend
2. Validate database schema against the specifications in Section 4
3. Test basic CRUD operations on all tables
4. Confirm authentication flow works (register, login, logout)
5. Verify deployment pipelines successfully build and deploy code changes

# Subtasks:
## 1. Provision and Configure Supabase Instance [done]
### Dependencies: None
### Description: Set up the Supabase project with authentication and security configurations
### Details:
1. Create a new Supabase project with appropriate plan for <PERSON> needs
2. Configure authentication settings (enable email/password and OAuth providers)
3. Set up row-level security policies for data protection
4. Create API keys and store them securely
5. Test authentication flow by creating a test user
6. Document the Supabase project URL, API keys, and configuration settings
7. Test the connection to Supabase using the generated API keys

## 2. Implement Database Schema and Migrations [done]
### Dependencies: 1.1
### Description: Define and create the core database tables, relationships, and indexes
### Details:
1. Create the following tables with appropriate columns and constraints:
   - users (id, email, name, avatar_url, created_at)
   - resources (id, name, description, url, logo_url, created_at, updated_at)
   - categories (id, name, description)
   - tags (id, name)
   - resource_categories (resource_id, category_id)
   - resource_tags (resource_id, tag_id)
   - reviews (id, resource_id, user_id, rating, comment, created_at)
   - submissions (id, user_id, resource_id, status, created_at)
   - saved_items (user_id, resource_id, created_at)
   - tool_requests (id, user_id, name, description, created_at, status)
2. Set up foreign key relationships between tables
3. Create indexes for frequently queried columns (e.g., resource_id, user_id)
4. Write and test SQL migrations
5. Create database triggers for updated_at timestamps
6. Test the schema with sample data insertion and queries
7. Document the schema design and relationships

## 3. Configure Deployment Infrastructure [pending]
### Dependencies: 1.1
### Description: Set up Vercel for frontend, cloud container service for backend, and establish CI/CD pipelines
### Details:
1. Create a new Vercel project for the Next.js frontend
2. Configure environment variables in Vercel (Supabase URL, API keys)
3. Set up a cloud container service (Google Cloud Run) for the NestJS backend
4. Create Dockerfile for the backend service
5. Configure environment variables for the backend service
6. Establish CI/CD pipelines for both frontend and backend:
   - Set up GitHub Actions workflows for testing and deployment
   - Configure automatic deployments on main branch merges
   - Set up preview deployments for pull requests
7. Test the deployment pipeline with a simple Hello World app
8. Document the deployment process, URLs, and environment configurations
9. Set up monitoring and logging for both services

## 4. Test RLS Policies with Different User Roles [pending]
### Dependencies: None
### Description: Verify all policies function correctly using test users with different roles (anonymous, authenticated, specific user).
### Details:


## 5. Create DELETE Policies for Owners/Admins [pending]
### Dependencies: None
### Description: Implement policies allowing users to delete their own content (e.g., reviews) or admins to delete records, checking ownership or admin role.
### Details:


## 6. Create UPDATE Policies for Owners/Admins [pending]
### Dependencies: None
### Description: Implement policies allowing users to update their own content (e.g., reviews) or admins to update records, checking ownership or admin role.
### Details:


## 7. Create INSERT Policies for Authenticated Users [pending]
### Dependencies: None
### Description: Implement policies allowing logged-in users to insert data into relevant tables (e.g., reviews, submissions, saved_items).
### Details:


## 8. Create SELECT Policies for Public/Authenticated Users [pending]
### Dependencies: None
### Description: Implement policies allowing public read access to non-sensitive data (e.g., resources, categories) and authenticated read access where appropriate.
### Details:


