# Task ID: 6
# Title: Implement User Contributions and Ratings
# Status: pending
# Dependencies: 2, 3
# Priority: medium
# Description: Develop functionality for user ratings, reviews, resource submissions, and the 'Request a Tool' feature.
# Details:
1. Implement rating and review system:
   - Backend API for submitting ratings (1-5 stars) and text reviews
   - Logic to calculate and update average ratings and review counts
   - Frontend form for submitting reviews on resource detail pages
   - Display of ratings and reviews on resource cards and detail pages
2. Develop resource submission functionality:
   - Submission form collecting essential resource data
   - Backend API to store submissions in 'pending' state
   - Validation logic for required fields
3. Create 'Request a Tool' feature:
   - Simple form for users to request missing tools
   - Backend storage for requests
   - Confirmation messaging after submission
4. Implement saved/bookmarked resources:
   - API for saving and unsaving resources
   - UI elements to indicate saved status
   - User profile page showing saved items

# Test Strategy:
1. Test rating submission and calculation logic
2. Verify review display and formatting
3. Test resource submission form validation
4. Verify 'Request a Tool' submissions are properly stored
5. Test saving/unsaving resources functionality
6. Verify user profile correctly displays saved items
7. Test authentication requirements for these features

# Subtasks:
## 1. Implement backend API for ratings and reviews [pending]
### Dependencies: None
### Description: Create the database models, controllers, and API endpoints for the rating and review system
### Details:
1. Design and implement database schema for ratings and reviews (including user_id, resource_id, rating value, review text, timestamps)
2. Create API endpoints for CRUD operations on ratings and reviews
3. Implement business logic for calculating and updating average ratings
4. Add validation for rating values (1-5) and review content
5. Implement authentication middleware to ensure only logged-in users can submit ratings
6. Write unit tests for the rating/review API endpoints

## 2. Develop frontend components for ratings and reviews [pending]
### Dependencies: 6.1
### Description: Create UI components for displaying and submitting ratings and reviews on resource pages
### Details:
1. Create a star rating component that allows users to both view and submit ratings
2. Build a review submission form with text input and validation
3. Implement review listing component with pagination for resource detail pages
4. Add rating summary display (average rating, total count) for resource cards and detail pages
5. Integrate with the backend API endpoints created in subtask 1
6. Add appropriate loading states and error handling
7. Ensure responsive design for all new components

## 3. Implement resource submission functionality [pending]
### Dependencies: None
### Description: Create the system for users to submit new resources for review
### Details:
1. Design and implement database schema for resource submissions with 'pending' status field
2. Create API endpoints for submitting and managing resource submissions
3. Implement validation logic for required fields (name, description, URL, category, etc.)
4. Build frontend submission form with all necessary fields and validation
5. Create admin interface for reviewing and approving/rejecting submissions
6. Add confirmation messaging and status tracking for users
7. Implement rate limiting to prevent spam submissions

## 4. Create 'Request a Tool' feature [pending]
### Dependencies: None
### Description: Implement functionality for users to request tools that aren't currently in the database
### Details:
1. Design and implement database schema for tool requests
2. Create API endpoint for submitting tool requests
3. Build simple frontend form with fields for tool name, description, and why it should be added
4. Implement form validation and submission handling
5. Create admin interface for viewing and managing tool requests
6. Add confirmation messaging after successful submission
7. Implement analytics to track most requested tools

## 5. Implement saved/bookmarked resources functionality [pending]
### Dependencies: None
### Description: Create system for users to save resources to their profile for later reference
### Details:
1. Design and implement database schema for saved resources (user_id, resource_id, timestamp)
2. Create API endpoints for saving, unsaving, and retrieving saved resources
3. Implement toggle button UI component for saving/unsaving resources
4. Add visual indicators for saved status on resource cards and detail pages
5. Create saved resources section on user profile page with filtering and sorting options
6. Implement pagination for saved resources list
7. Add ability to bulk manage (remove) saved resources

