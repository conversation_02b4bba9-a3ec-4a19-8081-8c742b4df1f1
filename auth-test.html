<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 0 20px; }
        .auth-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        #status { margin: 20px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Auth Test Page</h1>
    <div id="status">Status: Loading configuration...</div>

    <div class="auth-section">
        <h2>Email/Password Authentication</h2>
        <div>
            <input type="email" id="email" placeholder="Email">
            <input type="password" id="password" placeholder="Password">
            <button id="signUpButton">Sign Up</button>
            <button id="signInButton">Sign In</button>
            <button id="resetPasswordButton">Reset Password</button>
        </div>
    </div>

    <div class="auth-section">
        <h2>Google Authentication</h2>
        <button id="signInWithGoogleButton">Sign in with Google</button>
    </div>

    <div class="auth-section">
        <h2>Session Management</h2>
        <button id="signOutButton">Sign Out</button>
        <button id="getSessionButton">Get Current Session</button>
    </div>

    <script type="module">
        import config from './config.js';
        
        // Load environment variables from config
        let supabase;
        
        async function initializeSupabase() {
            try {
                if (!config.SUPABASE_URL || !config.SUPABASE_ANON_KEY) {
                    throw new Error('Missing Supabase configuration');
                }
                
                supabase = window.supabase.createClient(
                    config.SUPABASE_URL,
                    config.SUPABASE_ANON_KEY
                );
                window.supabaseClient = supabase; // Expose client instance for testing
                
                updateStatus('Configuration loaded, checking session...');
                await getSession();

                // Attach event listeners AFTER supabase is initialized and DOM is ready
                attachEventListeners();

            } catch (error) {
                updateStatus(`Configuration Error: ${error.message}`);
            }
        }

        function attachEventListeners() {
            document.getElementById('signUpButton').addEventListener('click', signUp);
            document.getElementById('signInButton').addEventListener('click', signIn);
            document.getElementById('resetPasswordButton').addEventListener('click', resetPassword);
            document.getElementById('signInWithGoogleButton').addEventListener('click', signInWithGoogle);
            document.getElementById('signOutButton').addEventListener('click', signOut);
            document.getElementById('getSessionButton').addEventListener('click', getSession);
            console.log("Event listeners attached");
        }

        async function updateStatus(message) {
            const statusEl = document.getElementById('status');
            if (statusEl) {
                 statusEl.textContent = `Status: ${message}`;
            } else {
                console.error("Status element not found!")
            }
        }

        async function signUp() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email,
                    password,
                });
                
                if (error) throw error;
                updateStatus(`Signed up! Check email for confirmation: ${email}`);
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
            }
        }

        async function signIn() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email,
                    password,
                });
                
                if (error) throw error;
                updateStatus(`Signed in as: ${data.user?.email || email}`);
                console.log("Sign in successful:", data);
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                console.error("Sign in error:", error);
            }
        }

        async function signInWithGoogle() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            try {
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                });
                
                if (error) throw error;
                updateStatus('Redirecting to Google...');
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
            }
        }

        async function resetPassword() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            const email = document.getElementById('email').value;
            
            try {
                const { data, error } = await supabase.auth.resetPasswordForEmail(email);
                
                if (error) throw error;
                updateStatus(`Password reset email sent to: ${email}`);
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
            }
        }

        async function signOut() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            try {
                const { error } = await supabase.auth.signOut();
                
                if (error) throw error;
                updateStatus('Signed out');
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
            }
        }

        async function getSession() {
            if (!supabase) {
                updateStatus('Error: Supabase not initialized');
                return;
            }
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) throw error;
                if (session) {
                    updateStatus(`Current session: ${session.user.email}`);
                } else {
                    updateStatus('No active session');
                }
                console.log("Get session result:", session);
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                console.error("Get session error:", error);
            }
        }

        // Initialize Supabase when the script loads
        initializeSupabase();
    </script>
</body>
</html> 