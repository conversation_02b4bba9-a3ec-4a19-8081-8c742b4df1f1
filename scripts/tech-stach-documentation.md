
AI Navigator - Technology Stack Documentation

Version: 1.0
Date: 2023-10-27 (Update as needed)

1. Overview

This document outlines the core technologies, frameworks, platforms, and services selected for the development and deployment of the AI Navigator platform. The goal is to provide a clear reference for the development team.

2. Core Pillars
2.1. Frontend

Framework: Next.js (v14+ App Router)

Language: TypeScript

UI Library: React

Rationale: Provides a robust framework for server-rendered and static React applications, excellent developer experience, built-in routing, image optimization, and strong integration with Vercel. App Router enables modern React features.

Styling:

Tailwind CSS: Utility-first CSS framework for rapid UI development.

Shadcn/ui: Re-usable component library built on Tailwind CSS and Radix UI for consistent and accessible UI elements.

State Management: Zustand

Rationale: Minimalist, unopinionated state management solution suitable for managing UI state without excessive boilerplate.

Data Fetching/Caching (Client-Side): TanStack Query (React Query v5)

Rationale: Handles server state management, caching, background updates, and synchronization efficiently, simplifying data fetching logic.

HTTP Client: Axios

Rationale: Mature and widely used promise-based HTTP client for making requests from the frontend (client-side or Next.js server-side components/API routes) to the backend API.

2.2. Backend

Framework: NestJS (v10+)

Language: TypeScript

Platform: Node.js (LTS version)

Rationale: Provides a structured, modular, and scalable architecture for building efficient server-side applications. Leverages TypeScript decorators and promotes SOLID principles. Excellent ecosystem and tooling.

Database ORM: Prisma (v5+)

Rationale: Modern, type-safe database toolkit for Node.js and TypeScript. Simplifies database access, migrations, and provides excellent autocompletion and type safety when interacting with the PostgreSQL database from NestJS.

2.3. Database & BaaS (Backend as a Service)

Primary Platform: Supabase

Rationale: Provides a comprehensive open-source backend solution, reducing the need to manage individual infrastructure components. Hosted platform simplifies operations.

Core Components Used:

Database: Managed PostgreSQL (v15+) - Robust, open-source relational database.

Vector Extension: pgvector (Enabled within Supabase Postgres) - For storing and querying vector embeddings for semantic search.

Authentication: Supabase Auth - Handles user sign-up, login (including Google OAuth), JWT management, and integration with database Row Level Security (RLS).

Storage: Supabase Storage (Optional, for future use) - If needed for storing user uploads (e.g., profile pictures, logos if not using URLs).

2.4. AI & Machine Learning

LLM Provider (Initial): Google Gemini API

Rationale: Provides powerful language models accessible via API for features like the AI Chatbot Assistant and potentially AI Content Enrichment. Chosen as a starting point.

Flexibility: Configured to potentially switch to other providers via tools like OpenRouter later.

Embeddings Model: OpenAI text-embedding-3-small

Rationale: State-of-the-art model for generating semantic vector embeddings with good performance and manageable dimensionality (1536). Used to create vectors stored in pgvector.

Vector Database: pgvector (within Supabase Postgres)

Rationale: Integrates vector storage and search directly into the primary PostgreSQL database, simplifying the tech stack compared to a separate vector DB initially.

2.5. Data Acquisition (Scraping Service)

Language: Python (v3.9+)

Rationale: Mature ecosystem and excellent libraries for web scraping and data processing.

Framework/Libraries: Scrapy / Playwright

Rationale: Scrapy for efficient crawling, Playwright for handling JavaScript-heavy sites if needed. Deployed as a separate service.

DB Interaction: SQLAlchemy or direct psycopg2 (if simple).

2.6. Background Job Processing

Queue System: BullMQ

Rationale: Robust, Redis-backed job queue system for Node.js, suitable for handling background tasks like sending emails, processing scraped data, or long-running AI tasks.

Queue Backend: Redis

Rationale: Fast in-memory data store required by BullMQ. Can be hosted via cloud providers or potentially within Supabase ecosystem add-ons if available.

3. Deployment & Infrastructure

Frontend Deployment: Vercel

Rationale: Seamless integration with Next.js, automatic CI/CD, global CDN, serverless functions, preview deployments.

Backend Deployment: Cloud Container Service (e.g., Render, Fly.io)

Rationale: Provides managed hosting for Dockerized applications like the NestJS backend. Render/Fly.io offer simpler starting points compared to AWS/GCP.

Containerization: Docker - Standardizes the backend application environment for development and deployment.

CI/CD: GitHub Actions (or GitLab CI/Bitbucket Pipelines)

Rationale: Automates testing, building, and deployment workflows directly from the code repository.

Database: Hosted via Supabase.

4. Development Practices

Primary Language: TypeScript (Frontend & Backend)

Rationale: Provides static typing, improving code quality, maintainability, and developer productivity.

Version Control: Git

Repository Hosting: GitHub (or GitLab/Bitbucket)

Package Management: npm / yarn / pnpm (Consistent choice within frontend/backend)

Environment Variables: Heavy reliance on environment variables (.env locally, platform settings in deployed environments) for configuration and secrets management.

5. Key Considerations

Environment Variable Management: Securely managing different sets of variables (Supabase keys, API keys, DB URLs, secrets) for local development, Vercel (frontend), and the Cloud Container Service (backend) is critical.

Database Migrations: Prisma Migrate (recommended due to NestJS/Prisma usage) or Supabase CLI Migrations should be used to manage schema changes after the initial setup.

Monitoring & Logging: Implement appropriate logging in NestJS and consider monitoring tools for deployed services.