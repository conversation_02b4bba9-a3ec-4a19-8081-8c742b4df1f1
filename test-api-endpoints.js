// Simple script to test API endpoints
const API_BASE_URL = 'https://ai-nav.onrender.com';

// Test endpoints
const endpoints = [
  'GET /users/me/profile',
  'GET /users/me/preferences', 
  'GET /users/me/submitted-tools',
  'GET /users/me/tool-requests',
  'PUT /users/me'
];

async function testEndpoint(method, path, token) {
  try {
    const response = await fetch(`${API_BASE_URL}${path}`, {
      method: method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`${method} ${path}: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const text = await response.text();
      console.log(`  Error: ${text.substring(0, 200)}`);
    }
    
    return response.status;
  } catch (error) {
    console.log(`${method} ${path}: ERROR - ${error.message}`);
    return null;
  }
}

// You'll need to replace this with a real token from your browser
const TEST_TOKEN = 'your-token-here';

async function testAllEndpoints() {
  console.log('Testing API endpoints...\n');
  
  for (const endpoint of endpoints) {
    const [method, path] = endpoint.split(' ');
    await testEndpoint(method, path, TEST_TOKEN);
  }
}

// Uncomment to run the test
// testAllEndpoints();

console.log('To test the endpoints:');
console.log('1. Get your access token from the browser (localStorage or session)');
console.log('2. Replace TEST_TOKEN with your actual token');
console.log('3. Uncomment the testAllEndpoints() call');
console.log('4. Run: node test-api-endpoints.js');
