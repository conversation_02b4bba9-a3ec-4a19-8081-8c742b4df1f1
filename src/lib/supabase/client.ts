"use client";

import { createBrowserClient } from '@supabase/ssr';
// import { Database } from '@/lib/database.types'; // If you have this defined

// This instance is for client-side usage (in Client Components, Contexts, etc.)
// It requires the NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY
// environment variables to be set.

// Note: The createBrowserClient is a light wrapper around the normal createClient that
// is safe to use in browser environments such as Client Components and other client-side code.
// It doesn't handle session persistence across server/client automatically; that's where
// middleware and route handlers come into play with @supabase/ssr.

export const createSupabaseBrowserClient = () => {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error("Missing env.NEXT_PUBLIC_SUPABASE_URL");
  }
  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error("Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY");
  }

  // return createBrowserClient<Database>( // Typed version
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
};

// For convenience, you might export an instance if you don't need to call the factory everywhere
// However, be mindful of when this module is evaluated if used outside of functions/components.
// const supabase = createSupabaseBrowserClient();
// export default supabase;
// It's often safer to call createSupabaseBrowserClient() where needed, or manage a singleton instance carefully.

// For now, let's export the factory function. Components that need a client will call it.

// If you also need a server-side client (e.g., in Route Handlers or Server Components
// where you handle cookies manually or use different auth strategies), you might create it here too,
// or in a separate file like `supabase/server.ts`.
// Example for server components (requires cookieStore from next/headers):
// import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
// import { cookies } from 'next/headers';
// export const supabaseServer = () => createServerComponentClient({ cookies }); 