'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ChevronDownIcon, ChevronUpIcon, SlidersHorizontal } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface AdvancedFiltersProps {
  // Boolean filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;

  // Array filters
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];

  // Date filters
  createdAtFrom?: string;
  createdAtTo?: string;

  // Location search
  locationSearch?: string;

  // Handlers
  onFilterChange: (filterName: string, value: boolean | string | string[] | null) => void;
  onClearAdvanced: () => void;
}

const PRICING_MODELS = [
  { value: 'FREE', label: 'Free' },
  { value: 'FREEMIUM', label: 'Freemium' },
  { value: 'SUBSCRIPTION', label: 'Subscription' },
  { value: 'PAY_PER_USE', label: 'Pay Per Use' },
  { value: 'ONE_TIME_PURCHASE', label: 'One-time Purchase' },
  { value: 'CONTACT_SALES', label: 'Contact Sales' },
  { value: 'OPEN_SOURCE', label: 'Open Source' },
];

const PRICE_RANGES = [
  { value: 'FREE', label: 'Free' },
  { value: 'LOW', label: 'Low' },
  { value: 'MEDIUM', label: 'Medium' },
  { value: 'HIGH', label: 'High' },
  { value: 'ENTERPRISE', label: 'Enterprise' },
];

const EMPLOYEE_COUNT_RANGES = [
  { value: 'C1_10', label: '1-10 employees' },
  { value: 'C11_50', label: '11-50 employees' },
  { value: 'C51_200', label: '51-200 employees' },
  { value: 'C201_500', label: '201-500 employees' },
  { value: 'C501_1000', label: '501-1000 employees' },
  { value: 'C1001_5000', label: '1001-5000 employees' },
  { value: 'C5001_PLUS', label: '5000+ employees' },
];

const FUNDING_STAGES = [
  { value: 'SEED', label: 'Seed' },
  { value: 'PRE_SEED', label: 'Pre-Seed' },
  { value: 'SERIES_A', label: 'Series A' },
  { value: 'SERIES_B', label: 'Series B' },
  { value: 'SERIES_C', label: 'Series C' },
  { value: 'SERIES_D_PLUS', label: 'Series D+' },
  { value: 'PUBLIC', label: 'Public' },
];

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  hasFreeTier,
  apiAccess,
  employeeCountRanges,
  fundingStages,
  pricingModels,
  priceRanges,
  createdAtFrom,
  createdAtTo,
  locationSearch,
  onFilterChange,
  onClearAdvanced,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Count active advanced filters
  const activeFiltersCount = [
    hasFreeTier,
    apiAccess,
    employeeCountRanges && employeeCountRanges.length > 0 ? 1 : 0,
    fundingStages && fundingStages.length > 0 ? 1 : 0,
    pricingModels && pricingModels.length > 0 ? 1 : 0,
    priceRanges && priceRanges.length > 0 ? 1 : 0,
    createdAtFrom,
    createdAtTo,
    locationSearch,
  ].filter(filter => filter !== undefined && filter !== null && filter !== '' && filter !== 0).length;

  const handleBooleanChange = (filterName: string, checked: boolean) => {
    onFilterChange(filterName, checked || null);
  };

  const handleMultiSelectChange = (filterName: string, value: string, currentValues: string[] = []) => {
    if (value === 'all') {
      onFilterChange(filterName, null);
      return;
    }

    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];

    onFilterChange(filterName, newValues.length > 0 ? newValues : null);
  };

  const handleInputChange = (filterName: string, value: string) => {
    onFilterChange(filterName, value || null);
  };

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button variant="outline" className="w-full justify-between">
          <div className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            Advanced Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          {isOpen ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
        </Button>
      </CollapsibleTrigger>
      
      <CollapsibleContent className="space-y-4 mt-4">
        {/* Boolean Filters */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">Features & Availability</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasFreeTier"
                checked={hasFreeTier || false}
                onCheckedChange={(checked) => handleBooleanChange('hasFreeTier', checked as boolean)}
              />
              <Label htmlFor="hasFreeTier" className="text-sm">Has Free Tier</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="apiAccess"
                checked={apiAccess || false}
                onCheckedChange={(checked) => handleBooleanChange('apiAccess', checked as boolean)}
              />
              <Label htmlFor="apiAccess" className="text-sm">API Access</Label>
            </div>
          </div>
        </div>

        {/* Date Filters */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">Date Range</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label className="text-sm">Created From</Label>
              <input
                type="date"
                value={createdAtFrom || ''}
                onChange={(e) => handleInputChange('createdAtFrom', e.target.value)}
                className="w-full px-3 py-1 text-sm border border-input rounded-md bg-background"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm">Created To</Label>
              <input
                type="date"
                value={createdAtTo || ''}
                onChange={(e) => handleInputChange('createdAtTo', e.target.value)}
                className="w-full px-3 py-1 text-sm border border-input rounded-md bg-background"
              />
            </div>
          </div>
        </div>

        {/* Location Search */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-foreground">Location</h4>
          <div className="space-y-2">
            <Label className="text-sm">Location Search</Label>
            <input
              type="text"
              placeholder="e.g., San Francisco, Remote"
              value={locationSearch || ''}
              onChange={(e) => handleInputChange('locationSearch', e.target.value)}
              className="w-full px-3 py-1 text-sm border border-input rounded-md bg-background"
            />
          </div>
        </div>

        {/* Multi-Select Filters */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground">Company & Business</h4>

          {/* Employee Count Ranges */}
          <div className="space-y-2">
            <Label className="text-sm">Employee Count</Label>
            <div className="flex flex-wrap gap-2">
              {EMPLOYEE_COUNT_RANGES.map(range => (
                <div key={range.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`employee-${range.value}`}
                    checked={employeeCountRanges?.includes(range.value) || false}
                    onCheckedChange={() => handleMultiSelectChange('employeeCountRanges', range.value, employeeCountRanges || [])}
                  />
                  <Label htmlFor={`employee-${range.value}`} className="text-xs">
                    {range.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Funding Stages */}
          <div className="space-y-2">
            <Label className="text-sm">Funding Stage</Label>
            <div className="flex flex-wrap gap-2">
              {FUNDING_STAGES.map(stage => (
                <div key={stage.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`funding-${stage.value}`}
                    checked={fundingStages?.includes(stage.value) || false}
                    onCheckedChange={() => handleMultiSelectChange('fundingStages', stage.value, fundingStages || [])}
                  />
                  <Label htmlFor={`funding-${stage.value}`} className="text-xs">
                    {stage.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pricing Filters */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-foreground">Pricing</h4>

          {/* Pricing Models */}
          <div className="space-y-2">
            <Label className="text-sm">Pricing Model</Label>
            <div className="flex flex-wrap gap-2">
              {PRICING_MODELS.map(model => (
                <div key={model.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`pricing-${model.value}`}
                    checked={pricingModels?.includes(model.value) || false}
                    onCheckedChange={() => handleMultiSelectChange('pricingModels', model.value, pricingModels || [])}
                  />
                  <Label htmlFor={`pricing-${model.value}`} className="text-xs">
                    {model.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Price Ranges */}
          <div className="space-y-2">
            <Label className="text-sm">Price Range</Label>
            <div className="flex flex-wrap gap-2">
              {PRICE_RANGES.map(range => (
                <div key={range.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`price-${range.value}`}
                    checked={priceRanges?.includes(range.value) || false}
                    onCheckedChange={() => handleMultiSelectChange('priceRanges', range.value, priceRanges || [])}
                  />
                  <Label htmlFor={`price-${range.value}`} className="text-xs">
                    {range.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Clear Button */}
        {activeFiltersCount > 0 && (
          <div className="pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={onClearAdvanced}
              className="text-xs"
            >
              Clear Advanced Filters
            </Button>
          </div>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default AdvancedFilters;
