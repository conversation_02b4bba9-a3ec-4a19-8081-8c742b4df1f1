import React from 'react';
import ResourceCardSkeleton from '@/components/resource/ResourceCardSkeleton';

interface ResourcesSkeletonProps {
  count?: number;
}

const ResourcesSkeleton: React.FC<ResourcesSkeletonProps> = ({ count = 6 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <ResourceCardSkeleton key={i} />
      ))}
    </div>
  );
};

export default ResourcesSkeleton;
