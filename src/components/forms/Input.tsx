"use client";

import React from 'react';
import { FieldError } from 'react-hook-form'; // Removed UseFormRegisterReturn as it's not directly used as a prop

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: FieldError | undefined;
  // register is spread via {...props} from the parent form using form.register()
  classNameContainer?: string;
  classNameLabel?: string;
  classNameInput?: string;
  classNameError?: string;
}

// Using React.forwardRef to correctly pass the ref to the underlying input element
export const Input: React.FC<InputProps> = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, type = "text", error, classNameContainer, classNameLabel, classNameInput, classNameError, ...props }, ref) => {
    
    // Default Tailwind classes, can be overridden by passed-in props if needed
    const defaultContainerClasses = "mb-4"; // Example default
    const defaultLabelClasses = "block mb-1 text-sm font-medium text-gray-700";
    const defaultInputClasses =
      "w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500";
    const defaultErrorClasses = "mt-1 text-xs text-red-600";

    return (
      <div className={classNameContainer || defaultContainerClasses}>
        <label htmlFor={props.id || props.name || label} className={classNameLabel || defaultLabelClasses}>
          {label}
        </label>
        <input
          type={type}
          id={props.id || props.name || label} // Ensure id is consistent for the label
          ref={ref} // Forwarding ref to the input element
          {...props} // Spread other props including those from react-hook-form's register
          className={`${classNameInput || defaultInputClasses} ${error ? 'border-red-500 focus:border-red-500' : ''}`}
          aria-invalid={error ? "true" : "false"}
        />
        {error && (
          <p role="alert" className={classNameError || defaultErrorClasses}>
            {error.message}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input'; // For better debugging in React DevTools

export default Input; // Default export 