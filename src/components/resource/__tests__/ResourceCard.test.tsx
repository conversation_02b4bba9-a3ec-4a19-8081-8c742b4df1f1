import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';
import ResourceCard from '../ResourceCard';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useAuth } from '@/contexts/AuthContext';
import { useShare } from '@/hooks/useShare';
import { Entity } from '@/types/entity';

// Mock the hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/contexts/BookmarkContext', () => ({
  useBookmarkContext: jest.fn(),
}));

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

jest.mock('@/hooks/useShare', () => ({
  useShare: jest.fn(),
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: unknown }) {
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ href, children, ...props }: { href: string; children: React.ReactNode; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

const mockEntity: Entity = {
  id: 'test-entity-1',
  slug: 'test-entity',
  name: 'Test Entity',
  shortDescription: 'A test entity for testing purposes',
  description: 'A longer description of the test entity',
  logoUrl: 'https://example.com/logo.png',
  websiteUrl: 'https://example.com',
  avgRating: 4.5,
  reviewCount: 10,
  entityType: {
    id: 'type-1',
    name: 'AI Tool',
    description: 'AI Tool type',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  categories: [
    {
      id: 'cat-1',
      name: 'Machine Learning',
      description: 'ML category',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  features: [
    {
      id: 'feat-1',
      name: 'Feature 1',
      description: 'First feature',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  tags: [
    {
      id: 'tag-1',
      name: 'Tag 1',
      description: 'First tag',
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  ],
  hasFreeTier: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('ResourceCard', () => {
  const mockPush = jest.fn();
  const mockToggleBookmark = jest.fn();
  const mockIsBookmarked = jest.fn();
  const mockShareEntity = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });

    (useBookmarkContext as jest.Mock).mockReturnValue({
      isBookmarked: mockIsBookmarked,
      toggleBookmark: mockToggleBookmark,
    });

    (useAuth as jest.Mock).mockReturnValue({
      session: { access_token: 'test-token' },
    });

    (useShare as jest.Mock).mockReturnValue({
      shareEntity: mockShareEntity,
    });

    mockIsBookmarked.mockReturnValue(false);
  });

  it('renders entity information correctly', () => {
    render(<ResourceCard entity={mockEntity} />);

    expect(screen.getByText('Test Entity')).toBeInTheDocument();
    expect(screen.getByText('A test entity for testing purposes')).toBeInTheDocument();
    expect(screen.getByText('AI Tool')).toBeInTheDocument();
    expect(screen.getByText('Machine Learning')).toBeInTheDocument();
    expect(screen.getByText('Free Tier')).toBeInTheDocument();
    expect(screen.getByText('4.5')).toBeInTheDocument();
    expect(screen.getByText('(10 reviews)')).toBeInTheDocument();
  });

  it('shows bookmark button as not bookmarked initially', () => {
    render(<ResourceCard entity={mockEntity} />);
    
    const bookmarkButton = screen.getByLabelText('Add bookmark');
    expect(bookmarkButton).toBeInTheDocument();
  });

  it('shows bookmark button as bookmarked when entity is bookmarked', () => {
    mockIsBookmarked.mockReturnValue(true);
    
    render(<ResourceCard entity={mockEntity} />);
    
    const bookmarkButton = screen.getByLabelText('Remove bookmark');
    expect(bookmarkButton).toBeInTheDocument();
  });

  it('calls toggleBookmark when bookmark button is clicked', async () => {
    render(<ResourceCard entity={mockEntity} />);
    
    const bookmarkButton = screen.getByLabelText('Add bookmark');
    fireEvent.click(bookmarkButton);

    await waitFor(() => {
      expect(mockToggleBookmark).toHaveBeenCalledWith('test-entity-1');
    });
  });

  it('redirects to login when bookmark is clicked without authentication', async () => {
    (useAuth as jest.Mock).mockReturnValue({
      session: null,
    });

    render(<ResourceCard entity={mockEntity} />);
    
    const bookmarkButton = screen.getByLabelText('Add bookmark');
    fireEvent.click(bookmarkButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/login');
    });
  });

  it('calls shareEntity when share button is clicked', async () => {
    render(<ResourceCard entity={mockEntity} />);
    
    const shareButton = screen.getByLabelText('Share resource');
    fireEvent.click(shareButton);

    await waitFor(() => {
      expect(mockShareEntity).toHaveBeenCalledWith(mockEntity);
    });
  });

  it('uses correct URLs for navigation', () => {
    render(<ResourceCard entity={mockEntity} />);
    
    const titleLink = screen.getByText('Test Entity').closest('a');
    const ctaLink = screen.getByText('View Details').closest('a');
    
    expect(titleLink).toHaveAttribute('href', '/entities/test-entity');
    expect(ctaLink).toHaveAttribute('href', '/entities/test-entity');
  });

  it('falls back to entity ID when slug is not available', () => {
    const entityWithoutSlug = { ...mockEntity, slug: undefined };
    
    render(<ResourceCard entity={entityWithoutSlug} />);
    
    const titleLink = screen.getByText('Test Entity').closest('a');
    expect(titleLink).toHaveAttribute('href', '/entities/test-entity-1');
  });

  it('handles bookmark loading state', async () => {
    mockToggleBookmark.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<ResourceCard entity={mockEntity} />);
    
    const bookmarkButton = screen.getByLabelText('Add bookmark');
    fireEvent.click(bookmarkButton);

    // Should show loading state
    expect(bookmarkButton).toBeDisabled();
    
    await waitFor(() => {
      expect(bookmarkButton).not.toBeDisabled();
    });
  });
});
