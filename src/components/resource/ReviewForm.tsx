'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { Button } from '@/components/ui/button';
// import { Textarea } from '@/components/ui/textarea'; // Assuming Shadcn textarea - Install if not present
// import { Input } from '@/components/ui/input'; // Assuming Shadcn input - Install if not present
import { Star } from 'lucide-react';

export interface ReviewFormData {
  rating: number;
  title?: string;
  text: string;
}

interface ReviewFormProps {
  entityId: string;
  onSubmitReview: (data: ReviewFormData) => Promise<void>; 
  isSubmitting: boolean;
  formError?: string | null; // To display submission errors from parent
  formSuccess?: string | null; // To display submission success from parent
}

const ReviewForm: React.FC<ReviewFormProps> = ({ onSubmitReview, isSubmitting, formError, formSuccess }) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset, 
  } = useForm<ReviewFormData>({
    defaultValues: {
      rating: 0,
      title: '',
      text: ''
    }
  });

  const [hoverRating, setHoverRating] = useState(0);
  const currentRating = watch('rating');
  const currentText = watch('text');

  const handleRating = (rate: number) => {
    setValue('rating', rate, { shouldValidate: true });
  };

  const internalOnSubmit: SubmitHandler<ReviewFormData> = async (data) => {
    await onSubmitReview(data); 
    // Parent will handle reset or success/error message display
  };

  // Reset form if submission was successful (indicated by formSuccess message)
  React.useEffect(() => {
    if (formSuccess) {
      reset();
    }
  }, [formSuccess, reset]);

  return (
    <form onSubmit={handleSubmit(internalOnSubmit)} className="space-y-6">
      <div>
        <label htmlFor="rating" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Your Rating*
        </label>
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <Star
              key={star}
              className={`w-7 h-7 cursor-pointer transition-colors
                ${(hoverRating || currentRating) >= star ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-500'}
                ${errors.rating ? 'text-red-400' : ''}`}
              fill={(hoverRating || currentRating) >= star ? 'currentColor' : 'none'}
              onClick={() => handleRating(star)}
              onMouseEnter={() => setHoverRating(star)}
              onMouseLeave={() => setHoverRating(0)}
            />
          ))}
        </div>
        <input type="hidden" {...register('rating', { required: true, min: 1, max: 5 })} />
        {errors.rating && <p className="mt-1 text-xs text-red-500">Rating is required (1-5 stars).</p>}
      </div>

      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Review Title (Optional)
        </label>
        {/* Using basic input as placeholder for Shadcn Input */}
        <input
          id="title"
          {...register('title')}
          placeholder="e.g., Great tool for beginners!"
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
      </div>

      <div>
        <label htmlFor="text" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Your Review*
        </label>
        {/* Using basic textarea as placeholder for Shadcn Textarea */}
        <textarea
          id="text"
          {...register('text', {
            required: 'Review text cannot be empty.',
            minLength: {
              value: 10,
              message: 'Review must be at least 10 characters long.'
            }
          })}
          rows={5}
          placeholder="Share your experience... (minimum 10 characters)"
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
        <div className="mt-1 flex justify-between items-center">
          <div>
            {errors.text && <p className="text-xs text-red-500">{errors.text.message}</p>}
          </div>
          <p className={`text-xs ${(currentText?.length || 0) >= 10 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {currentText?.length || 0}/10 characters minimum
          </p>
        </div>
      </div>

      {formError && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Review Submission Error
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                {formError}
              </div>
            </div>
          </div>
        </div>
      )}
      {formSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800 dark:text-green-200">
                {formSuccess}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting || !!formSuccess} size="lg"> {/* Disable if already successfully submitted */}
          {isSubmitting ? 'Submitting...' : 'Submit Review'}
        </Button>
      </div>
    </form>
  );
};

export default ReviewForm; 