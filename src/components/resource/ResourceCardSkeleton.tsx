import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

const ResourceCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 flex flex-col h-full">
      {/* Image Section Skeleton */}
      <Skeleton className="h-40 w-full mb-4 rounded-lg" />

      {/* Title Skeleton */}
      <Skeleton className="h-6 w-3/4 mb-2" />

      {/* Badges Skeleton */}
      <div className="flex gap-2 mb-4">
        <Skeleton className="h-5 w-16 rounded-full" />
        <Skeleton className="h-5 w-20 rounded-full" />
      </div>

      {/* Description Skeleton */}
      <div className="space-y-2 flex-grow mb-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>

      {/* Rating Skeleton */}
      <div className="flex items-center gap-2 mb-4">
        <Skeleton className="h-4 w-4 rounded" />
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-16" />
      </div>

      {/* Tags Skeleton */}
      <div className="flex gap-1 mb-4">
        <Skeleton className="h-5 w-12 rounded" />
        <Skeleton className="h-5 w-16 rounded" />
        <Skeleton className="h-5 w-14 rounded" />
      </div>

      {/* CTA Button Skeleton */}
      <Skeleton className="h-10 w-full rounded-lg" />
    </div>
  );
};

export default ResourceCardSkeleton;
