'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Wrench,
  Plus,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  X,
  Edit,
  Eye
} from 'lucide-react';
import { UserSubmittedTool } from '@/types/profile';
import { getUserSubmittedTools } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';


export default function MyToolsSection() {
  const { session, isLoading: isAuthLoading } = useAuth();
  const [submittedTools, setSubmittedTools] = useState<UserSubmittedTool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch submitted tools
  useEffect(() => {
    const fetchSubmittedTools = async () => {
      // DO NOT FETCH if auth state is still being determined
      if (isAuthLoading) {
        return;
      }

      if (!session?.access_token) {
        setSubmittedTools([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Remove pagination parameters as backend doesn't support them
        const response = await getUserSubmittedTools(session.access_token);

        // CRITICAL FIX: Check for valid response structure
        if (response && response.data && Array.isArray(response.data)) {
          setSubmittedTools(response.data);
        } else {
          console.error('Invalid response structure from getUserSubmittedTools', response);
          setSubmittedTools([]);
        }
      } catch (apiError) {
        console.error('Submitted tools API failed:', apiError);

        // Don't show error for authentication issues
        if (apiError instanceof Error && apiError.message.includes('Unauthorized')) {
          console.log('[MyToolsSection] Authentication error, setting empty tools');
          setSubmittedTools([]);
        } else {
          setError(apiError instanceof Error ? apiError.message : 'Failed to load submitted tools');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubmittedTools();
  }, [session?.access_token, isAuthLoading]); // Add isAuthLoading to dependency array

  // Get status badge variant
  const getStatusBadge = (status: UserSubmittedTool['submission_status']) => {
    switch (status) {
      case 'PENDING':
        return { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' };
      case 'UNDER_REVIEW':
        return { variant: 'outline' as const, icon: AlertCircle, color: 'text-blue-600' };
      case 'APPROVED':
        return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
      case 'REJECTED':
        return { variant: 'destructive' as const, icon: X, color: 'text-red-600' };
      case 'PUBLISHED':
        return { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' };
      default:
        return { variant: 'secondary' as const, icon: Clock, color: 'text-gray-600' };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-48 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="text-red-500">
              <Wrench className="h-12 w-12 mx-auto mb-4" />
            </div>
            <h3 className="text-lg font-semibold">Failed to Load Submitted Tools</h3>
            <p className="text-gray-600">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <CardTitle className="flex items-center">
                <Wrench className="h-5 w-5 mr-2" />
                My Submitted Tools ({submittedTools.length})
              </CardTitle>
              <CardDescription>
                Track the status of AI tools you&apos;ve submitted to the platform
              </CardDescription>
            </div>
            
            <Button asChild>
              <Link href="/submit">
                <Plus className="h-4 w-4 mr-2" />
                Submit New Tool
              </Link>
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {submittedTools.length === 0 ? (
            <div className="text-center py-12">
              <Wrench className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No submitted tools yet</h3>
              <p className="text-gray-600 mb-4">
                Share AI tools with the community by submitting them for review
              </p>
              <Button asChild>
                <Link href="/submit">
                  <Plus className="h-4 w-4 mr-2" />
                  Submit Your First Tool
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {submittedTools.map((submittedTool) => {
                const statusBadge = getStatusBadge(submittedTool.submission_status);
                const StatusIcon = statusBadge.icon;
                const entity = submittedTool.entity;

                return (
                  <Card key={submittedTool.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="pt-6">
                      {/* Entity Image/Icon */}
                      <div className="mb-4">
                        {entity.logoUrl ? (
                          <img
                            src={entity.logoUrl}
                            alt={entity.name}
                            className="h-16 w-16 rounded-lg object-cover mx-auto"
                          />
                        ) : (
                          <div className="h-16 w-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto">
                            <Wrench className="h-8 w-8 text-indigo-600" />
                          </div>
                        )}
                      </div>

                      {/* Tool Info */}
                      <div className="text-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {entity.name}
                        </h3>
                        <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                          {entity.description}
                        </p>
                        
                        {/* Status Badge */}
                        <Badge variant={statusBadge.variant} className="flex items-center justify-center space-x-1 w-fit mx-auto">
                          <StatusIcon className={`h-3 w-3 ${statusBadge.color}`} />
                          <span className="capitalize">{submittedTool.submission_status.replace('_', ' ')}</span>
                        </Badge>
                      </div>

                      {/* Categories */}
                      <div className="flex flex-wrap gap-1 justify-center mb-4">
                        {entity.categories.slice(0, 2).map((category) => (
                          <Badge key={category.id} variant="outline" className="text-xs">
                            {category.name}
                          </Badge>
                        ))}
                        {entity.categories.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{entity.categories.length - 2}
                          </Badge>
                        )}
                      </div>

                      {/* Submission Details */}
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center justify-between">
                          <span>Submitted:</span>
                          <span>{new Date(submittedTool.submitted_at).toLocaleDateString()}</span>
                        </div>
                        
                        {submittedTool.reviewed_at && (
                          <div className="flex items-center justify-between">
                            <span>Reviewed:</span>
                            <span>{new Date(submittedTool.reviewed_at).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>

                      {/* Reviewer Notes */}
                      {submittedTool.reviewer_notes && (
                        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <p className="text-sm text-blue-800">
                            <strong>Review Notes:</strong> {submittedTool.reviewer_notes}
                          </p>
                        </div>
                      )}

                      {/* Changes Requested */}
                      {submittedTool.changes_requested && (
                        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <p className="text-sm text-yellow-800">
                            <strong>Changes Requested:</strong> {submittedTool.changes_requested}
                          </p>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="mt-4 flex justify-center space-x-2">
                        {submittedTool.submission_status === 'PUBLISHED' && (
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/entities/${entity.slug}`}>
                              <Eye className="h-4 w-4 mr-1" />
                              View Live
                            </Link>
                          </Button>
                        )}
                        
                        {entity.websiteUrl && (
                          <Button variant="ghost" size="sm" asChild>
                            <a
                              href={entity.websiteUrl}
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              <ExternalLink className="h-4 w-4 mr-1" />
                              Website
                            </a>
                          </Button>
                        )}
                        
                        {(submittedTool.submission_status === 'REJECTED' ||
                          submittedTool.changes_requested) && (
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {/* Summary Stats */}
          {submittedTools.length > 0 && (
            <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {submittedTools.filter(t => t.submission_status === 'PUBLISHED').length}
                </div>
                <div className="text-sm text-gray-600">Published</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {submittedTools.filter(t => ['PENDING', 'UNDER_REVIEW'].includes(t.submission_status)).length}
                </div>
                <div className="text-sm text-gray-600">Under Review</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {submittedTools.filter(t => t.submission_status === 'APPROVED').length}
                </div>
                <div className="text-sm text-gray-600">Approved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {submittedTools.filter(t => t.submission_status === 'REJECTED').length}
                </div>
                <div className="text-sm text-gray-600">Rejected</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
