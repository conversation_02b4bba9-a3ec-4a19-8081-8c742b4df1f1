"use client";

import React from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Input from '@/components/forms/Input';
import { Button } from "@/components/ui/button";
import { registerUser } from '@/services/api';
import { RegisterData } from '@/types/user';
import { createSupabaseBrowserClient } from "@/lib/supabase/client";
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const registerSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }).min(1, { message: "Email is required" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: z.string().min(1, { message: "Please confirm your password" }),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

type RegisterFormInputs = z.infer<typeof registerSchema>;

export default function RegisterForm() {
  const router = useRouter();
  const supabaseClient = createSupabaseBrowserClient();
  const { setAuthError } = useAuth();
  const { handleSubmit, formState, register: formRegister, setError, clearErrors } = useForm<RegisterFormInputs>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    }
  });
  const { errors, isSubmitting } = formState;

  const onSubmit: SubmitHandler<RegisterFormInputs> = async (data) => {
    setAuthError(null);
    clearErrors("root.serverError");
    const registrationData: RegisterData = {
      email: data.email,
      password: data.password
    };

    try {
      await registerUser(registrationData);

      if (registrationData.email) {
        sessionStorage.setItem('registrationEmail', registrationData.email);
      }
      router.push('/auth/check-email'); 

    } catch (error: unknown) {
      console.error("[RegisterForm] Registration submission error:", error);
      const specificMessage = error instanceof Error ? error.message : "An unexpected error occurred. Please try again.";
      setAuthError(specificMessage);
      setError("root.serverError", {
        type: "custom",
        message: specificMessage,
      });
    }
  };

  const handleGoogleSignIn = async () => {
    setAuthError(null);
    clearErrors("root.serverError");

    // PRIORITY 3 FIX: Enhanced OAuth redirect with next parameter for better UX
    const redirectUrl = new URL(`${window.location.origin}/auth/callback`);
    redirectUrl.searchParams.set('next', '/profile'); // Redirect to profile after successful registration

    const { error } = await supabaseClient.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUrl.toString(),
      },
    });
    if (error) {
      console.error('[RegisterForm] Google Sign In error:', error);
      setAuthError(error.message);
      setError("root.serverError", {
        type: "custom",
        message: error.message,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full max-w-md p-8 bg-white shadow-lg rounded-lg">
      <h2 className="text-3xl font-bold text-center text-gray-800">Create Account</h2>
      {errors.root?.serverError && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {errors.root.serverError.message}
        </div>
      )}
      {errors.root && !errors.root.serverError && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {errors.root.message}
        </div>
      )}
      <div>
        <Input
          label="Email address"
          type="email"
          {...formRegister("email")}
          error={errors.email}
          classNameContainer="mb-4"
          classNameInput="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500"
          classNameLabel="block mb-1 text-sm font-medium text-gray-700"
          classNameError="mt-1 text-xs text-red-600"
          autoComplete="email"
          placeholder="<EMAIL>"
          disabled={isSubmitting}
        />
      </div>

      <div>
        <Input
          label="Password (min. 8 characters)"
          type="password"
          {...formRegister("password")}
          error={errors.password}
          classNameContainer="mb-4"
          classNameInput="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500"
          classNameLabel="block mb-1 text-sm font-medium text-gray-700"
          classNameError="mt-1 text-xs text-red-600"
          autoComplete="new-password"
          placeholder="••••••••"
          disabled={isSubmitting}
        />
      </div>

      <div>
        <Input
          label="Confirm Password"
          type="password"
          {...formRegister("confirmPassword")}
          error={errors.confirmPassword}
          classNameContainer="mb-6"
          classNameInput="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500"
          classNameLabel="block mb-1 text-sm font-medium text-gray-700"
          classNameError="mt-1 text-xs text-red-600"
          autoComplete="new-password"
          placeholder="••••••••"
          disabled={isSubmitting}
        />
      </div>

      <Button 
        type="submit" 
        disabled={isSubmitting}
        className="w-full px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg focus:ring-4 focus:ring-blue-300 transition-transform active:scale-95 disabled:opacity-50"
      >
        {isSubmitting ? 'Creating account...' : 'Create Account'}
      </Button>

      <div className="relative flex py-3 items-center mt-2">
        <div className="flex-grow border-t border-gray-300"></div>
        <span className="flex-shrink mx-4 text-gray-500 text-sm">Or continue with</span>
        <div className="flex-grow border-t border-gray-300"></div>
      </div>

      <Button 
        type="button" 
        onClick={handleGoogleSignIn}
        className="w-full px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg focus:ring-4 focus:ring-blue-300 transition-transform active:scale-95 flex items-center justify-center space-x-2"
        disabled={isSubmitting}
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M22.56,12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26,1.37-1.04,2.53-2.21,3.31v2.77h3.57c2.08-1.92,3.28-4.74,3.28-8.09Z" fill="#4285F4"/>
          <path d="M12,23c2.97,0,5.46-.98,7.28-2.66l-3.57-2.77c-.98,.66-2.23,1.06-3.71,1.06-2.86,0-5.29-1.93-6.16-4.53H2.18v2.84C3.99,20.53,7.7,23,12,23Z" fill="#34A853"/>
          <path d="M5.84,14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43,.35-2.09V7.07H2.18C1.43,8.55,1,10.22,1,12s.43,3.45,1.18,4.93l2.85-2.22.81-.62Z" fill="#FBBC05"/>
          <path d="M12,5.38c1.62,0,3.06,.56,4.21,1.64l3.15-3.15C17.45,2.29,14.97,1,12,1,7.7,1,3.99,3.47,2.18,7.07l3.66,2.84c.87-2.6,3.3-4.53,6.16-4.53Z" fill="#EA4335"/>
        </svg>
        <span>Sign in with Google</span>
      </Button>

      <p className="text-sm text-center text-gray-600 pt-4">
        Already have an account? <Link href="/login" className="font-medium text-blue-600 hover:underline">Log in</Link>
      </p>
    </form>
  );
} 