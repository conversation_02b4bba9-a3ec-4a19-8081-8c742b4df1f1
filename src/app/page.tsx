'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>R<PERSON>, Search, Sparkles, Zap, Target } from 'lucide-react';
import Chatbot from '@/components/chatbot/Chatbot';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Discover the Perfect{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                AI Tools
              </span>{' '}
              for Your Needs
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Get personalized AI tool recommendations through our intelligent assistant.
              Simply describe what you&apos;re looking for, and we&apos;ll find the perfect match.
            </p>

            {/* Quick Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button asChild size="lg" className="text-lg px-8 py-3">
                <Link href="#ai-assistant">
                  <Sparkles className="mr-2 h-5 w-5" />
                  Try AI Assistant
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-lg px-8 py-3">
                <Link href="/browse">
                  <Search className="mr-2 h-5 w-5" />
                  Browse All Tools
                </Link>
              </Button>
            </div>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100 mx-auto mb-4">
                  <Zap className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">AI-Powered Recommendations</h3>
                <p className="text-gray-600">Get personalized suggestions based on your specific needs and requirements.</p>
              </div>
              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 mx-auto mb-4">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Curated Database</h3>
                <p className="text-gray-600">Access our comprehensive collection of verified AI tools and resources.</p>
              </div>
              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-amber-100 mx-auto mb-4">
                  <Search className="h-6 w-6 text-amber-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Discovery</h3>
                <p className="text-gray-600">Find tools you never knew existed with our intelligent search capabilities.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Assistant Section */}
      <section id="ai-assistant" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <Chatbot />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to explore more?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Browse our full catalog of AI tools or submit your own discoveries.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/browse">
                Browse All Tools
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/submit">
                Submit a Tool
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
