'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const API_BASE_URL = 'https://ai-nav.onrender.com';

export default function ApiTestPage() {
  const [results, setResults] = useState<Record<string, {
    status?: number;
    statusText?: string;
    data?: unknown;
    error?: string;
    success: boolean;
  }>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  // You'll need to get a real token from your browser's localStorage or session
  const [token, setToken] = useState('');

  const testEndpoint = async (name: string, method: string, path: string, body?: unknown) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    
    try {
      const fetchOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
      };

      if (body) {
        fetchOptions.body = JSON.stringify(body);
      }

      const response = await fetch(`${API_BASE_URL}${path}`, fetchOptions);

      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
          success: response.ok,
        },
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [name]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const endpoints = [
    { name: 'Sync Profile', method: 'POST', path: '/auth/sync-profile' },
    { name: 'Get Profile', method: 'GET', path: '/users/me/profile' },
    { name: 'Update Profile', method: 'PUT', path: '/users/me', body: { display_name: 'Test User' } },
    { name: 'Get Preferences', method: 'GET', path: '/users/me/preferences' },
    { name: 'Get My Tools', method: 'GET', path: '/users/me/submitted-tools' },
    { name: 'Get Tool Requests', method: 'GET', path: '/users/me/tool-requests' },
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">API Endpoint Test</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Token</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Access Token (get from browser localStorage or session):
            </label>
            <input
              type="text"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              placeholder="Bearer token..."
              className="w-full p-2 border rounded"
            />
            <p className="text-xs text-gray-600">
              Open browser dev tools → Application/Storage → Local Storage → look for Supabase session
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4">
        {endpoints.map((endpoint) => (
          <Card key={endpoint.name}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{endpoint.name}</span>
                <Button
                  onClick={() => testEndpoint(endpoint.name, endpoint.method, endpoint.path, endpoint.body)}
                  disabled={loading[endpoint.name]}
                  size="sm"
                >
                  {loading[endpoint.name] ? 'Testing...' : 'Test'}
                </Button>
              </CardTitle>
              <p className="text-sm text-gray-600">
                {endpoint.method} {endpoint.path}
              </p>
            </CardHeader>
            <CardContent>
              {results[endpoint.name] && (
                <div className="space-y-2">
                  <div className={`p-2 rounded text-sm ${
                    results[endpoint.name].success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    Status: {results[endpoint.name].status || 'Error'} {results[endpoint.name].statusText}
                  </div>
                  <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                    {JSON.stringify(results[endpoint.name].data || results[endpoint.name].error, null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
