'use client';

import dynamic from 'next/dynamic';
import React from 'react'; // React is needed for Suspense

// Fallback component for Suspense
const LoadingFallback = () => (
  <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-4">
    <p>Loading error information...</p>
  </div>
);

// Dynamically import the component that uses useSearchParams
const AuthCodeErrorDisplayComponent = dynamic( // Renamed to avoid conflict if original page had same name
  () => import('@/components/auth/AuthCodeErrorDisplay'),
  {
    ssr: false, // Ensure it's client-side only
    loading: () => <LoadingFallback /> // This loading is for the dynamic import itself
  }
);

export default function AuthCodeErrorPage() {
  return (
    <React.Suspense fallback={<LoadingFallback />}>
      <AuthCodeErrorDisplayComponent />
    </React.Suspense>
  );
} 