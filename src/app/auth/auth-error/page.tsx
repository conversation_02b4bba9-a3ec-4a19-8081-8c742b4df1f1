'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const errorMessage = searchParams.get('error');

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '80vh', textAlign: 'center' }}>
      <h1>Authentication Error</h1>
      <p style={{ color: 'red', marginBottom: '20px' }}>
        {errorMessage || 'An unexpected error occurred during authentication.'}
      </p>
      <Link href="/login">
        <button style={{ padding: '10px 20px', cursor: 'pointer' }}>Try Logging In</button>
      </Link>
      <br />
      <Link href="/">
        <button style={{ padding: '10px 20px', cursor: 'pointer', marginTop: '10px' }}>Go to Homepage</button>
      </Link>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense fallback={
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '80vh', textAlign: 'center' }}>
        <h1>Authentication Error</h1>
        <p>Loading...</p>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  );
}