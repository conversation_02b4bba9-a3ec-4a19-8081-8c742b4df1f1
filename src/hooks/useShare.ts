import { useCallback } from 'react';
import { Entity } from '@/types/entity';

interface ShareData {
  title: string;
  text: string;
  url: string;
}

interface UseShareReturn {
  shareEntity: (entity: Entity) => Promise<void>;
  copyToClipboard: (text: string) => Promise<boolean>;
  getShareUrl: (entity: Entity) => string;
  getSocialShareUrls: (entity: Entity) => {
    twitter: string;
    linkedin: string;
    facebook: string;
    reddit: string;
  };
}

export const useShare = (): UseShareReturn => {
  const getShareUrl = useCallback((entity: Entity): string => {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com';
    return `${baseUrl}/entities/${entity.slug}`;
  }, []);

  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        return success;
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }, []);

  const getSocialShareUrls = useCallback((entity: Entity) => {
    const url = getShareUrl(entity);
    const title = `Check out ${entity.name} on AI Navigator`;
    // const description = entity.shortDescription || entity.description || `Discover ${entity.name}`;

    return {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      reddit: `https://reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
    };
  }, [getShareUrl]);

  const shareEntity = useCallback(async (entity: Entity): Promise<void> => {
    const shareData: ShareData = {
      title: `${entity.name} | AI Navigator`,
      text: entity.shortDescription || entity.description || `Check out ${entity.name} on AI Navigator`,
      url: getShareUrl(entity),
    };

    try {
      // Try native Web Share API first (mobile browsers)
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        return;
      }
    } catch (error) {
      console.log('Native share failed, falling back to clipboard:', error);
    }

    // Fallback to copying URL to clipboard
    const success = await copyToClipboard(shareData.url);
    if (success) {
      // You could show a toast notification here
      console.log('URL copied to clipboard');
    } else {
      throw new Error('Failed to share or copy URL');
    }
  }, [getShareUrl, copyToClipboard]);

  return {
    shareEntity,
    copyToClipboard,
    getShareUrl,
    getSocialShareUrls,
  };
};
