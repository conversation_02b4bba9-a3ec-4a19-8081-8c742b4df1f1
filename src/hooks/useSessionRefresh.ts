import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

/**
 * Hook to detect and handle session refresh after OAuth callback
 * This is part of Priority 3 improvements for better Google Sign-Up flow
 */
export const useSessionRefresh = () => {
  const searchParams = useSearchParams();
  const [sessionRefreshed, setSessionRefreshed] = useState(false);

  useEffect(() => {
    const sessionRefreshParam = searchParams?.get('session_refreshed');
    if (sessionRefreshParam === 'true') {
      console.log('[useSessionRefresh] Detected session_refreshed parameter');
      setSessionRefreshed(true);
      
      // Clean up URL parameter
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        url.searchParams.delete('session_refreshed');
        window.history.replaceState({}, '', url.toString());
      }
    }
  }, [searchParams]);

  return {
    sessionRefreshed,
    clearSessionRefreshed: () => setSessionRefreshed(false),
  };
};
