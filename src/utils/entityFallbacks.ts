import { Entity } from '@/types/entity';

export interface FallbackConfig {
  field: string;
  fallbackValue?: unknown;
  generator?: (entity: Entity) => unknown;
  condition?: (entity: Entity) => boolean;
}

// Smart fallback configurations for different entity fields
export const ENTITY_FALLBACKS: FallbackConfig[] = [
  // Basic information fallbacks
  {
    field: 'shortDescription',
    generator: (entity: Entity) => {
      if (entity.description) {
        // Extract first sentence or first 150 characters
        const firstSentence = entity.description.split('.')[0];
        if (firstSentence.length > 150) {
          return entity.description.substring(0, 147) + '...';
        }
        return firstSentence + '.';
      }
      return `Discover ${entity.name} and explore its features.`;
    },
  },
  {
    field: 'description',
    generator: (entity: Entity) => {
      if (entity.shortDescription) {
        return entity.shortDescription;
      }
      return `${entity.name} is a ${entity.entityType.name.toLowerCase()} in the AI ecosystem. Learn more about its features and capabilities.`;
    },
  },
  {
    field: 'logoUrl',
    generator: (entity: Entity) => {
      // Generate a placeholder logo URL based on entity name
      const initials = entity.name
        .split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .join('')
        .substring(0, 2);
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=6366f1&color=ffffff&size=200`;
    },
  },

  // SEO fallbacks
  {
    field: 'metaTitle',
    generator: (entity: Entity) => {
      const entityType = entity.entityType.name;
      return `${entity.name} | ${entityType} | AI Navigator`;
    },
  },
  {
    field: 'metaDescription',
    generator: (entity: Entity) => {
      const description = entity.shortDescription || entity.description;
      if (description) {
        return description.length > 160 
          ? description.substring(0, 157) + '...'
          : description;
      }
      return `Discover ${entity.name}, a ${entity.entityType.name.toLowerCase()} featured on AI Navigator. Explore features, reviews, and more.`;
    },
  },

  // Rating and review fallbacks
  {
    field: 'avgRating',
    fallbackValue: 0,
    condition: (entity: Entity) => entity.reviewCount === 0,
  },
  {
    field: 'reviewCount',
    fallbackValue: 0,
  },
  {
    field: 'saveCount',
    fallbackValue: 0,
  },

  // Company information fallbacks
  {
    field: 'foundedYear',
    generator: (entity: Entity) => {
      // Try to extract year from createdAt as a rough estimate
      const createdYear = new Date(entity.createdAt).getFullYear();
      // Don't use current year as fallback, return null instead
      return createdYear < new Date().getFullYear() ? createdYear : null;
    },
  },
  {
    field: 'locationSummary',
    generator: (entity: Entity) => {
      // Try to infer location from website URL
      if (entity.websiteUrl) {
        try {
          const url = new URL(entity.websiteUrl);
          const domain = url.hostname.toLowerCase();
          
          // Common country-specific domains
          if (domain.endsWith('.co.uk')) return 'United Kingdom';
          if (domain.endsWith('.de')) return 'Germany';
          if (domain.endsWith('.fr')) return 'France';
          if (domain.endsWith('.ca')) return 'Canada';
          if (domain.endsWith('.au')) return 'Australia';
          if (domain.endsWith('.jp')) return 'Japan';
          
          // Default for .com and others
          return 'Global';
        } catch {
          return 'Global';
        }
      }
      return 'Location not specified';
    },
  },

  // Technical details fallbacks
  {
    field: 'details.hasFreeTier',
    generator: (entity: Entity) => {
      // Infer from pricing model if available
      const pricingModel = entity.details?.pricingModel;
      if (pricingModel === 'FREE' || pricingModel === 'FREEMIUM') {
        return true;
      }
      return false;
    },
  },
  {
    field: 'details.pricingModel',
    generator: (entity: Entity) => {
      // Try to infer from hasFreeTier
      if (entity.hasFreeTier || entity.details?.hasFreeTier) {
        return 'FREEMIUM';
      }
      // Default to contact sales for enterprise tools
      return 'CONTACT_SALES';
    },
  },
  {
    field: 'details.priceRange',
    generator: (entity: Entity) => {
      const pricingModel = entity.details?.pricingModel;
      if (pricingModel === 'FREE') return 'FREE';
      if (pricingModel === 'FREEMIUM') return 'LOW';
      if (entity.hasFreeTier) return 'LOW';
      return 'MEDIUM'; // Conservative default
    },
  },

  // Array field fallbacks
  {
    field: 'categories',
    fallbackValue: [],
  },
  {
    field: 'tags',
    fallbackValue: [],
  },
  {
    field: 'features',
    fallbackValue: [],
  },
  {
    field: 'details.integrations',
    fallbackValue: [],
  },
  {
    field: 'details.supportedOs',
    generator: () => {
      // Default to web-based for most AI tools
      return ['Web'];
    },
  },
];

/**
 * Applies smart fallbacks to an entity object
 */
export function applyEntityFallbacks(entity: Entity): Entity {
  const enhancedEntity = { ...entity };

  for (const fallback of ENTITY_FALLBACKS) {
    // Check if fallback should be applied
    if (fallback.condition && !fallback.condition(entity)) {
      continue;
    }

    const fieldPath = fallback.field.split('.');
    let current: Record<string, unknown> = enhancedEntity as Record<string, unknown>;

    // Navigate to the field location
    for (let i = 0; i < fieldPath.length - 1; i++) {
      const key = fieldPath[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key] as Record<string, unknown>;
    }

    const finalKey = fieldPath[fieldPath.length - 1];
    const currentValue = fieldPath.length === 1 ? (enhancedEntity as Record<string, unknown>)[finalKey] : current[finalKey];

    // Apply fallback if field is missing, null, undefined, or empty
    if (currentValue === null || currentValue === undefined || currentValue === '' || 
        (Array.isArray(currentValue) && currentValue.length === 0)) {
      
      let fallbackValue;
      
      if (fallback.generator) {
        fallbackValue = fallback.generator(entity);
      } else {
        fallbackValue = fallback.fallbackValue;
      }

      // Only apply if we have a valid fallback value
      if (fallbackValue !== null && fallbackValue !== undefined) {
        if (fieldPath.length === 1) {
          (enhancedEntity as Record<string, unknown>)[finalKey] = fallbackValue;
        } else {
          current[finalKey] = fallbackValue;
        }
      }
    }
  }

  return enhancedEntity;
}

/**
 * Gets a fallback value for a specific field
 */
export function getFieldFallback(entity: Entity, fieldPath: string): unknown {
  const fallback = ENTITY_FALLBACKS.find(f => f.field === fieldPath);
  
  if (!fallback) {
    return null;
  }

  if (fallback.condition && !fallback.condition(entity)) {
    return null;
  }

  if (fallback.generator) {
    return fallback.generator(entity);
  }

  return fallback.fallbackValue;
}

/**
 * Checks if a field has a smart fallback available
 */
export function hasFallback(fieldPath: string): boolean {
  return ENTITY_FALLBACKS.some(f => f.field === fieldPath);
}

/**
 * Enhanced field accessor that includes fallback logic
 */
export function getFieldWithFallback(entity: Entity, fieldPath: string): unknown {
  const keys = fieldPath.split('.');
  let value: unknown = entity;
  
  // Navigate to the field
  for (const key of keys) {
    if (value && typeof value === 'object' && key in (value as Record<string, unknown>)) {
      value = (value as Record<string, unknown>)[key];
    } else {
      value = undefined;
      break;
    }
  }

  // If no value found, try fallback
  if (value === null || value === undefined || value === '' || 
      (Array.isArray(value) && value.length === 0)) {
    return getFieldFallback(entity, fieldPath);
  }

  return value;
}
