'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useBookmarks } from '@/hooks/useBookmarks';

interface BookmarkContextType {
  bookmarkedEntityIds: Set<string>;
  isBookmarked: (entityId: string) => boolean;
  toggleBookmark: (entityId: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  refreshBookmarks: () => Promise<void>;
}

const BookmarkContext = createContext<BookmarkContextType | undefined>(undefined);

interface BookmarkProviderProps {
  children: ReactNode;
}

export const BookmarkProvider: React.FC<BookmarkProviderProps> = ({ children }) => {
  const bookmarkState = useBookmarks();

  return (
    <BookmarkContext.Provider value={bookmarkState}>
      {children}
    </BookmarkContext.Provider>
  );
};

export const useBookmarkContext = (): BookmarkContextType => {
  const context = useContext(BookmarkContext);
  if (context === undefined) {
    throw new Error('useBookmarkContext must be used within a BookmarkProvider');
  }
  return context;
};
