import { PrismaUserProfile } from './user'; // Assuming user info will be populated
import { PaginationMeta } from './entity'; // Import PaginationMeta

export interface Review {
  id: string; // or number
  rating: number;
  title?: string; // Optional title
  text: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  user?: PrismaUserProfile; // Populated user details, adjust if API returns minimal user info
  userId?: string; // Or just the ID if user object isn't fully populated
  entityId: string; // The ID of the entity this review belongs to
  status?: string; // e.g., 'APPROVED', 'PENDING' - consider an enum
}

export interface PaginatedReviews {
  data: Review[];
  meta: PaginationMeta;
}

// If the API returns paginated reviews, similar to entities:
// export interface PaginatedReviews {
//   data: Review[];
//   meta: PaginationMeta; // Re-use from entity.ts or define here if different
// }

// For submitting a new review (based on CreateReviewDto)
export interface CreateReviewPayload {
  entityId: string;
  rating: number;
  title?: string;
  text: string;
}

// For updating an existing review (based on UpdateReviewDto)
export interface UpdateReviewPayload {
  rating?: number;
  title?: string;
  text?: string;
} 