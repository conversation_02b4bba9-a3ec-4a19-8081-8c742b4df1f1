import { User<PERSON><PERSON>, UserStatus, TechnicalLevel } from './enums';

export interface PrismaUserProfile {
  id: string;
  auth_user_id: string; // This usually refers to the Supabase auth user ID
  username: string | null;
  display_name: string | null;
  email: string; // Typically unique and matches Supabase auth email
  role: UserRole;
  status: UserStatus;
  technical_level: TechnicalLevel | null;
  profile_picture_url: string | null;
  bio: string | null;
  social_links: Record<string, string> | null; // Changed 'any' to 'string' for better type safety, adjust if needed
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  last_login: string | null; // ISO date string or null
} 

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData extends LoginCredentials {
  displayName?: string; // Optional display name during registration
  // Add any other registration fields if necessary
} 