import { Entity } from './entity';

/**
 * Response interface for the /recommendations endpoint
 */
export interface RecommendationResponse {
  /** Array of recommended entities based on the user's problem description */
  recommendedEntities: Entity[];
  /** AI-generated explanation of why these entities were recommended */
  explanation: string;
}

/**
 * Payload interface for requesting recommendations
 */
export interface RecommendationRequest {
  /** User's problem description or query */
  problem_description: string;
  /** Optional filters to apply to the recommendation search */
  filters?: {
    entityTypes?: string[];
    categories?: string[];
    tags?: string[];
    features?: string[];
    priceRange?: string[];
    [key: string]: unknown;
  };
}

/**
 * Error response interface for recommendation API calls
 */
export interface RecommendationError {
  message: string;
  details?: string;
  code?: string;
}
