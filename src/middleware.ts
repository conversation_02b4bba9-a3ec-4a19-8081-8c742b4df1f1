import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

// UUID regex pattern to detect ID-based URLs
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if this is an old ID-based entity URL and redirect to browse page
  if (pathname.startsWith('/entities/')) {
    const segments = pathname.split('/');
    const entityIdentifier = segments[2];

    // If it looks like a UUID, redirect to browse page with a message
    if (entityIdentifier && UUID_REGEX.test(entityIdentifier)) {
      const url = request.nextUrl.clone();
      url.pathname = '/browse';
      url.searchParams.set('redirected', 'true');
      url.searchParams.set('reason', 'old-url');

      return NextResponse.redirect(url, 301); // Permanent redirect for SEO
    }
  }

  // Create an unmodified response
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // If the cookie is to be deleted, we actually need to tell NextResponse.next() to delete it
          // by setting it to an empty value and maxAge to 0.
          // The request.cookies.set method is for updating cookies on the incoming request.
          request.cookies.set({
            name,
            value,
            ...options,
          });
          // The response object is used to set cookies on the outgoing response.
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          // request.cookies.delete(name); // This method might not exist or work as expected for all cases
          request.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Refresh session if expired - important for Server Components
  // Next.js Middleware runs on every request for matched paths.
  const { data: { user } } = await supabase.auth.getUser();

  // Optional: Redirect to login if user is not authenticated and trying to access protected routes
  // if (!user && request.nextUrl.pathname.startsWith('/dashboard')) {
  //   return NextResponse.redirect(new URL('/login', request.url));
  // }

  console.log('[Middleware] Running (Restored). User fetched:', user?.id);

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - /auth (auth routes like /auth/callback, /auth/confirm)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|auth|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}; 