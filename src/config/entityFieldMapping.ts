import { Entity } from '@/types/entity';
import { ReactNode } from 'react';

export interface FieldConfig {
  key: string;
  label: string;
  type: 'text' | 'url' | 'email' | 'date' | 'number' | 'boolean' | 'array' | 'rating' | 'badge' | 'social' | 'price' | 'custom';
  section: 'overview' | 'features' | 'pricing' | 'contact' | 'technical' | 'company';
  priority: number; // Lower numbers = higher priority
  icon?: string; // Lucide icon name
  fallback?: string | (() => string);
  formatter?: (value: unknown) => string | ReactNode;
  condition?: (entity: Entity) => boolean; // Show field only if condition is true
  accessor?: (entity: Entity) => unknown; // Custom accessor for nested fields
}

export const COMMON_ENTITY_FIELDS: FieldConfig[] = [
  // Overview Section
  {
    key: 'name',
    label: 'Name',
    type: 'text',
    section: 'overview',
    priority: 1,
    icon: 'Type',
  },
  {
    key: 'description',
    label: 'Description',
    type: 'text',
    section: 'overview',
    priority: 2,
    icon: 'FileText',
  },
  {
    key: 'shortDescription',
    label: 'Summary',
    type: 'text',
    section: 'overview',
    priority: 3,
    icon: 'AlignLeft',
    fallback: 'No summary available',
  },
  {
    key: 'websiteUrl',
    label: 'Website',
    type: 'url',
    section: 'overview',
    priority: 4,
    icon: 'Globe',
  },
  {
    key: 'avgRating',
    label: 'Rating',
    type: 'rating',
    section: 'overview',
    priority: 5,
    icon: 'Star',
    formatter: (value: unknown) => `${Number(value).toFixed(1)} / 5.0`,
  },
  {
    key: 'reviewCount',
    label: 'Reviews',
    type: 'number',
    section: 'overview',
    priority: 6,
    icon: 'MessageSquare',
    formatter: (value: unknown) => {
      const num = Number(value);
      return `${num} review${num !== 1 ? 's' : ''}`;
    },
  },

  // Company Section
  {
    key: 'foundedYear',
    label: 'Founded',
    type: 'number',
    section: 'company',
    priority: 1,
    icon: 'Calendar',
    fallback: 'Not specified',
  },
  {
    key: 'employeeCountRange',
    label: 'Team Size',
    type: 'badge',
    section: 'company',
    priority: 2,
    icon: 'Users',
    formatter: (value: unknown) => {
      const stringValue = String(value);
      const ranges: Record<string, string> = {
        'C1_10': '1-10 employees',
        'C11_50': '11-50 employees',
        'C51_200': '51-200 employees',
        'C201_500': '201-500 employees',
        'C501_1000': '501-1000 employees',
        'C1001_5000': '1001-5000 employees',
        'C5001_PLUS': '5000+ employees',
      };
      return ranges[stringValue] || stringValue;
    },
  },
  {
    key: 'fundingStage',
    label: 'Funding Stage',
    type: 'badge',
    section: 'company',
    priority: 3,
    icon: 'TrendingUp',
    formatter: (value: unknown) => {
      const stringValue = String(value);
      const stages: Record<string, string> = {
        'SEED': 'Seed',
        'PRE_SEED': 'Pre-Seed',
        'SERIES_A': 'Series A',
        'SERIES_B': 'Series B',
        'SERIES_C': 'Series C',
        'SERIES_D_PLUS': 'Series D+',
        'PUBLIC': 'Public',
      };
      return stages[stringValue] || stringValue;
    },
  },
  {
    key: 'locationSummary',
    label: 'Location',
    type: 'text',
    section: 'company',
    priority: 4,
    icon: 'MapPin',
    fallback: 'Location not specified',
  },

  // Contact Section
  {
    key: 'documentationUrl',
    label: 'Documentation',
    type: 'url',
    section: 'contact',
    priority: 1,
    icon: 'BookOpen',
  },
  {
    key: 'contactUrl',
    label: 'Contact',
    type: 'url',
    section: 'contact',
    priority: 2,
    icon: 'Mail',
  },
  {
    key: 'privacyPolicyUrl',
    label: 'Privacy Policy',
    type: 'url',
    section: 'contact',
    priority: 3,
    icon: 'Shield',
  },
  {
    key: 'socialLinks',
    label: 'Social Media',
    type: 'social',
    section: 'contact',
    priority: 4,
    icon: 'Share2',
  },

  // Features Section
  {
    key: 'categories',
    label: 'Categories',
    type: 'array',
    section: 'features',
    priority: 1,
    icon: 'Layers',
    accessor: (entity: Entity) => entity.categories?.map(cat => cat.name),
  },
  {
    key: 'tags',
    label: 'Tags',
    type: 'array',
    section: 'features',
    priority: 2,
    icon: 'Tag',
    accessor: (entity: Entity) => entity.tags?.map(tag => tag.name),
  },
  {
    key: 'features',
    label: 'Key Features',
    type: 'array',
    section: 'features',
    priority: 3,
    icon: 'Zap',
    accessor: (entity: Entity) => entity.features?.map(feature => feature.name),
  },
];

// Entity type specific field configurations
export const ENTITY_TYPE_SPECIFIC_FIELDS: Record<string, FieldConfig[]> = {
  'tool': [
    {
      key: 'hasFreeTier',
      label: 'Free Tier Available',
      type: 'boolean',
      section: 'pricing',
      priority: 1,
      icon: 'DollarSign',
      accessor: (entity: Entity) => entity.hasFreeTier || entity.details?.hasFreeTier,
    },
    {
      key: 'apiAccess',
      label: 'API Access',
      type: 'boolean',
      section: 'technical',
      priority: 1,
      icon: 'Code',
      accessor: (entity: Entity) => entity.details?.apiAccess,
    },
    {
      key: 'pricingModel',
      label: 'Pricing Model',
      type: 'badge',
      section: 'pricing',
      priority: 2,
      icon: 'CreditCard',
      accessor: (entity: Entity) => entity.details?.pricingModel,
      formatter: (value: unknown) => {
        const stringValue = String(value);
        const models: Record<string, string> = {
          'FREE': 'Free',
          'FREEMIUM': 'Freemium',
          'SUBSCRIPTION': 'Subscription',
          'PAY_PER_USE': 'Pay per Use',
          'ONE_TIME': 'One-time Purchase',
          'CONTACT_SALES': 'Contact Sales',
          'OPEN_SOURCE': 'Open Source',
        };
        return models[stringValue] || stringValue;
      },
    },
    {
      key: 'priceRange',
      label: 'Price Range',
      type: 'price',
      section: 'pricing',
      priority: 3,
      icon: 'DollarSign',
      accessor: (entity: Entity) => entity.details?.priceRange,
    },
    {
      key: 'integrations',
      label: 'Integrations',
      type: 'array',
      section: 'technical',
      priority: 2,
      icon: 'Link',
      accessor: (entity: Entity) => entity.details?.integrations,
    },
    {
      key: 'supportedOs',
      label: 'Supported Platforms',
      type: 'array',
      section: 'technical',
      priority: 3,
      icon: 'Monitor',
      accessor: (entity: Entity) => entity.details?.supportedOs,
    },
  ],
  'platform': [
    {
      key: 'keyServices',
      label: 'Key Services',
      type: 'array',
      section: 'features',
      priority: 1,
      icon: 'Server',
      accessor: (entity: Entity) => entity.details?.keyServices,
    },
    {
      key: 'supportedRegions',
      label: 'Supported Regions',
      type: 'array',
      section: 'technical',
      priority: 1,
      icon: 'Globe',
      accessor: (entity: Entity) => entity.details?.supportedRegions,
    },
  ],
  'course': [
    {
      key: 'duration',
      label: 'Duration',
      type: 'text',
      section: 'overview',
      priority: 7,
      icon: 'Clock',
      accessor: (entity: Entity) => entity.details?.duration,
    },
    {
      key: 'difficulty',
      label: 'Difficulty Level',
      type: 'badge',
      section: 'overview',
      priority: 8,
      icon: 'BarChart',
      accessor: (entity: Entity) => entity.details?.difficulty,
    },
  ],
};

export function getFieldsForEntity(entity: Entity): FieldConfig[] {
  const commonFields = COMMON_ENTITY_FIELDS;
  const typeSpecificFields = ENTITY_TYPE_SPECIFIC_FIELDS[entity.entityType.slug] || [];
  
  return [...commonFields, ...typeSpecificFields]
    .filter(field => !field.condition || field.condition(entity))
    .sort((a, b) => {
      // Sort by section first, then by priority
      if (a.section !== b.section) {
        const sectionOrder = ['overview', 'features', 'pricing', 'technical', 'company', 'contact'];
        return sectionOrder.indexOf(a.section) - sectionOrder.indexOf(b.section);
      }
      return a.priority - b.priority;
    });
}

export function getFieldValue(entity: Entity, field: FieldConfig): unknown {
  if (field.accessor) {
    return field.accessor(entity);
  }

  // Handle nested field access
  const keys = field.key.split('.');
  let value: unknown = entity;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in (value as Record<string, unknown>)) {
      value = (value as Record<string, unknown>)[key];
    } else {
      value = undefined;
      break;
    }
  }

  return value;
}
