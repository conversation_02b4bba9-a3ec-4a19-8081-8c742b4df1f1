describe('Authentication Tests', () => {
  beforeEach(() => {
    // Visit the page before each test
    cy.visit('/auth-test.html')
    // Wait for config to load initially - increases reliability
    cy.get('#status', { timeout: 10000 }).should('not.contain', 'Loading configuration...')
    // Start each test assuming no session
    cy.get('#status').should(statusElement => {
      expect(statusElement.text()).to.match(/Status: (No active session|Current session: .+|Signed out)/)
    })
    // Optional: Clear inputs if needed, though dynamic emails avoid conflicts
    cy.get('#email').clear()
    cy.get('#password').clear()
  })

  it('should load the page and initialize Supabase', () => {
    // Verify Supabase initialized without errors and shows no active session initially
    cy.get('#status').should('not.contain', 'Error')
    cy.get('#status').should('contain', 'No active session')
  })

  it('should handle sign up process', () => {
    // This test remains the same as it already uses dynamic credentials
    const testEmail = `test-signup-${Date.now()}@example.com`
    const testPassword = 'testPassword123!'

    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign Up').click()

    // Check for success message
    cy.get('#status', { timeout: 10000 })
      .should('contain', 'Check email for confirmation')
      .and('contain', testEmail)
  })

  it('should handle sign in process', () => {
    // Dynamically create user first
    const testEmail = `test-signin-${Date.now()}@example.com`
    const testPassword = 'testPassword123!'

    // Sign Up
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign Up').click()
    cy.get('#status', { timeout: 10000 }).should('contain', 'Check email for confirmation') // Wait for sign up feedback

    // Clear fields before signing in
    cy.get('#email').clear()
    cy.get('#password').clear()

    // Sign In with the same credentials
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign In').click()

    // Check for sign-in success message
    // Note: Supabase might require email confirmation by default.
    // If sign-in fails here with "Email not confirmed", you might need to
    // disable email confirmation in your Supabase project settings for testing.
    cy.get('#status', { timeout: 10000 })
      .should('contain', 'Signed in as')
      .and('contain', testEmail)
  })

  it('should handle sign out process', () => {
    // Dynamically create and sign in user first
    const testEmail = `test-signout-${Date.now()}@example.com`
    const testPassword = 'testPassword123!'

    // Sign Up
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign Up').click()
    cy.get('#status', { timeout: 10000 }).should('contain', 'Check email for confirmation')

    // Clear fields
    cy.get('#email').clear()
    cy.get('#password').clear()

    // Sign In
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign In').click()
    cy.get('#status', { timeout: 10000 }).should('contain', 'Signed in as') // Wait for sign in

    // Then Sign Out
    cy.contains('button', 'Sign Out').click()

    // Verify signed out state
    cy.get('#status', { timeout: 10000 }).should('contain', 'Signed out')
  })

  it('should handle session management', () => {
    // Dynamically create and sign in user
    const testEmail = `test-session-${Date.now()}@example.com`
    const testPassword = 'testPassword123!'

    // Check initial session status
    cy.contains('button', 'Get Current Session').click()
    cy.get('#status').should('contain', 'No active session')

    // Sign Up
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign Up').click()
    cy.get('#status', { timeout: 10000 }).should('contain', 'Check email for confirmation')

    // Clear fields
    cy.get('#email').clear()
    cy.get('#password').clear()

    // Sign In
    cy.get('#email').type(testEmail)
    cy.get('#password').type(testPassword)
    cy.contains('button', 'Sign In').click()
    cy.get('#status', { timeout: 10000 }).should('contain', 'Signed in as') // Wait for sign in

    // Check session after sign in
    cy.contains('button', 'Get Current Session').click()
    cy.get('#status', { timeout: 10000 })
      .should('contain', 'Current session')
      .and('contain', testEmail)
  })

  it('should attempt to initiate Google OAuth flow', () => {
    // Ensure Supabase client is available on the window
    cy.window().should('have.property', 'supabaseClient')

    // Stub the signInWithOAuth method before clicking the button
    cy.window().its('supabaseClient.auth').then(auth => {
      // Make the stub return a resolved promise to simulate success
      cy.stub(auth, 'signInWithOAuth').resolves({ data: { user: null, session: null }, error: null }).as('googleOAuthStub');
    });

    // Click the Google Sign-In button
    cy.get('#signInWithGoogleButton').click()

    // Assert that the stubbed method was called correctly
    cy.get('@googleOAuthStub').should('have.been.calledOnceWith', { 
      provider: 'google' 
    })

    // Check that the status message indicates redirection attempt
    cy.get('#status').should('contain', 'Redirecting to Google...')
  })
}) 