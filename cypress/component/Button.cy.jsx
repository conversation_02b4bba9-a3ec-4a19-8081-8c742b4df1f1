import Button from '../../components/Button'

describe('Button Component', () => {
  it('renders with text', () => {
    cy.mount(<Button>Click me</Button>)
    cy.get('button').should('have.text', 'Click me')
  })

  it('handles click events', () => {
    const onClickSpy = cy.spy().as('onClickSpy')
    cy.mount(<Button onClick={onClickSpy}>Click me</Button>)
    cy.get('button').click()
    cy.get('@onClickSpy').should('have.been.called')
  })
}) 