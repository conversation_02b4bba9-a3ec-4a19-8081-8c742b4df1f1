import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// ES modules fix for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

// Serve config with environment variables (Define specific routes BEFORE static)
app.get('/config.js', (req, res) => {
    console.log('>>> Request received for /config.js route handler');
    const configData = {
        SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    };
    console.log('>>> Sending config:', configData);
    
    // Send as JavaScript module instead of JSON
    res.setHeader('Content-Type', 'application/javascript');
    res.send(`const config = ${JSON.stringify(configData, null, 2)};\n\nexport default config;`);
});

console.log('--- Defining express.static middleware ---');
// Serve static files (Now handles requests not caught by specific routes above)
app.use(express.static(path.join(__dirname)));

// Root route - redirect to auth test page
app.get('/', (req, res) => {
    res.redirect('/auth-test.html');
});

// Error handling middleware
app.use((req, res) => {
    res.status(404).send('Page not found');
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`Auth test page available at http://localhost:${port}/auth-test.html`);
}); 